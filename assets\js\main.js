// ===== MAIN JAVASCRIPT FILE =====
// Web Agency - Main functionality and interactions

$(document).ready(function() {
    // Initialize all components
    initNavigation();
    initHeroSlider();
    initWorkSlider();
    initScrollAnimations();
    initCookieBanner();
    initSmoothScrolling();
    initScrollToTop();
    initCounters();
    
    // Register GSAP plugins
    gsap.registerPlugin(ScrollTrigger);
});

// ===== NAVIGATION =====
function initNavigation() {
    const navbar = $('#navbar');
    const navToggle = $('#nav-toggle');
    const navMenu = $('#nav-menu');
    const navMenuClose = $('#nav-menu-close');
    const navLinks = $('.nav-link');

    // Navbar scroll effect
    $(window).scroll(function() {
        if ($(window).scrollTop() > 50) {
            navbar.addClass('scrolled');
        } else {
            navbar.removeClass('scrolled');
        }
    });

    // Mobile menu toggle
    navToggle.click(function() {
        $(this).toggleClass('active');
        navMenu.toggleClass('active');
        $('body').toggleClass('menu-open');
    });

    // Mobile menu close button
    navMenuClose.click(function() {
        navToggle.removeClass('active');
        navMenu.removeClass('active');
        $('body').removeClass('menu-open');
    });

    // Close mobile menu when clicking on links
    navLinks.click(function() {
        navToggle.removeClass('active');
        navMenu.removeClass('active');
        $('body').removeClass('menu-open');
    });

    // Close mobile menu when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('.navbar').length) {
            navToggle.removeClass('active');
            navMenu.removeClass('active');
            $('body').removeClass('menu-open');
        }
    });
    
    // Active link highlighting
    updateActiveLink();
    $(window).scroll(updateActiveLink);
    
    function updateActiveLink() {
        const scrollPos = $(window).scrollTop() + 100;
        
        $('section[id]').each(function() {
            const section = $(this);
            const sectionTop = section.offset().top;
            const sectionHeight = section.outerHeight();
            const sectionId = section.attr('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                navLinks.removeClass('active');
                $(`.nav-link[href="#${sectionId}"]`).addClass('active');
            }
        });
    }
}

// ===== HERO SLIDER =====
function initHeroSlider() {
    if ($('.hero-slider').length) {
        const heroSwiper = new Swiper('.hero-slider', {
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            on: {
                slideChange: function() {
                    // Animate slide content
                    gsap.fromTo('.hero-content', 
                        { opacity: 0, y: 50 },
                        { opacity: 1, y: 0, duration: 1, ease: 'power2.out' }
                    );
                }
            }
        });
    }
}

// ===== WORK SLIDER =====
function initWorkSlider() {
    if ($('.work-slider').length) {
        const workSwiper = new Swiper('.work-slider', {
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            slidesPerView: 1,
            spaceBetween: 30,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                768: {
                    slidesPerView: 2,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 40,
                }
            }
        });
    }
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    // Fade in animations for service cards
    gsap.utils.toArray('.service-card').forEach((card, index) => {
        gsap.fromTo(card, 
            { opacity: 0, y: 50 },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                delay: index * 0.2,
                scrollTrigger: {
                    trigger: card,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    });
    
    // Fade in animations for work cards
    gsap.utils.toArray('.work-card').forEach((card, index) => {
        gsap.fromTo(card, 
            { opacity: 0, scale: 0.9 },
            {
                opacity: 1,
                scale: 1,
                duration: 0.6,
                delay: index * 0.1,
                scrollTrigger: {
                    trigger: card,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    });
    
    // Section titles animation
    gsap.utils.toArray('.section-title').forEach(title => {
        gsap.fromTo(title, 
            { opacity: 0, y: 30 },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                scrollTrigger: {
                    trigger: title,
                    start: 'top 85%',
                    end: 'bottom 15%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    });
    
    // CTA section animation
    if ($('.cta').length) {
        gsap.fromTo('.cta-content', 
            { opacity: 0, y: 50 },
            {
                opacity: 1,
                y: 0,
                duration: 1,
                scrollTrigger: {
                    trigger: '.cta',
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    }
}

// ===== COOKIE BANNER =====
function initCookieBanner() {
    const cookieBanner = $('#cookie-banner');
    const acceptBtn = $('#cookie-accept');
    const rejectBtn = $('#cookie-reject');
    const settingsBtn = $('#cookie-settings');
    
    // Check if user has already made a choice
    if (!getCookie('cookieConsent')) {
        setTimeout(() => {
            cookieBanner.fadeIn();
        }, 1000);
    }
    
    // Accept all cookies
    acceptBtn.click(function() {
        setCookie('cookieConsent', 'accepted', 365);
        setCookie('analyticsConsent', 'true', 365);
        setCookie('marketingConsent', 'true', 365);
        cookieBanner.fadeOut();
        
        // Initialize analytics if accepted
        if (typeof initAnalytics === 'function') {
            initAnalytics();
        }
        
        // Track consent
        trackEvent('cookie_consent', 'accept_all');
    });
    
    // Reject all cookies
    rejectBtn.click(function() {
        setCookie('cookieConsent', 'rejected', 365);
        setCookie('analyticsConsent', 'false', 365);
        setCookie('marketingConsent', 'false', 365);
        cookieBanner.fadeOut();
        
        // Track consent
        trackEvent('cookie_consent', 'reject_all');
    });
    
    // Cookie settings (simplified - opens modal in real implementation)
    settingsBtn.click(function() {
        alert('Cookie settings would open a detailed preferences modal in a full implementation.');
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    $('a[href^="#"]').click(function(e) {
        e.preventDefault();
        
        const target = $(this.getAttribute('href'));
        if (target.length) {
            const offsetTop = target.offset().top - 80; // Account for fixed navbar
            
            $('html, body').animate({
                scrollTop: offsetTop
            }, 800, 'easeInOutQuart');
        }
    });
}

// ===== SCROLL TO TOP =====
function initScrollToTop() {
    // Create scroll to top button
    $('body').append('<button id="scroll-to-top" class="scroll-to-top"><i class="fas fa-arrow-up"></i></button>');
    
    const scrollToTopBtn = $('#scroll-to-top');
    
    $(window).scroll(function() {
        if ($(window).scrollTop() > 300) {
            scrollToTopBtn.addClass('visible');
        } else {
            scrollToTopBtn.removeClass('visible');
        }
    });
    
    scrollToTopBtn.click(function() {
        $('html, body').animate({ scrollTop: 0 }, 800, 'easeInOutQuart');
    });
}

// ===== COUNTERS =====
function initCounters() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const countTo = $this.data('count');
        
        gsap.fromTo($this, 
            { textContent: 0 },
            {
                textContent: countTo,
                duration: 2,
                ease: 'power2.out',
                snap: { textContent: 1 },
                scrollTrigger: {
                    trigger: $this,
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                },
                onUpdate: function() {
                    $this.text(Math.ceil($this.text()));
                }
            }
        );
    });
}

// ===== UTILITY FUNCTIONS =====
function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function trackEvent(eventName, eventAction, eventLabel = '') {
    if (typeof gtag !== 'undefined' && getCookie('analyticsConsent') === 'true') {
        gtag('event', eventAction, {
            event_category: eventName,
            event_label: eventLabel
        });
    }
}

// ===== CV DOWNLOAD FUNCTION =====
function openCV(filename) {
    // In a real implementation, this would download the actual CV file
    const cvUrl = `assets/documents/${filename}`;
    window.open(cvUrl, '_blank');
    
    // Track CV download
    trackEvent('cv_download', 'download', filename);
}

// ===== EASING FUNCTIONS =====
$.easing.easeInOutQuart = function (x, t, b, c, d) {
    if ((t/=d/2) < 1) return c/2*t*t*t*t + b;
    return -c/2 * ((t-=2)*t*t*t - 2) + b;
};
