/* ===== RESPONSIVE BREAKPOINTS ===== */
/* 
Mobile: 320px - 767px
Tablet: 768px - 1023px
Desktop: 1024px+
*/

/* ===== TABLET STYLES (768px - 1023px) ===== */
@media (max-width: 1023px) {
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .page-title {
        font-size: var(--font-size-4xl);
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-xl);
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xl);
    }
    
    .cta-title {
        font-size: var(--font-size-3xl);
    }
    
    .work-image {
        height: 220px;
    }
}

/* ===== MOBILE STYLES (320px - 767px) ===== */
@media (max-width: 767px) {
    /* Typography adjustments */
    h1 { font-size: var(--font-size-3xl); }
    h2 { font-size: var(--font-size-2xl); }
    h3 { font-size: var(--font-size-xl); }
    h4 { font-size: var(--font-size-lg); }
    
    .container {
        padding: 0 var(--spacing-md);
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background-color: var(--navy-blue);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: left var(--transition-normal);
        z-index: var(--z-modal);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .nav-link {
        font-size: var(--font-size-xl);
        padding: var(--spacing-md);
    }
    
    .nav-toggle {
        display: flex;
        z-index: var(--z-modal-backdrop);
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Hero Section */
    .hero {
        height: 100vh;
        min-height: 500px;
    }
    
    .hero-slide {
        height: 100vh;
        min-height: 500px;
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }
    
    /* Page Header */
    .page-header {
        padding: calc(70px + var(--spacing-2xl)) 0 var(--spacing-2xl);
    }
    
    .page-title {
        font-size: var(--font-size-2xl);
    }
    
    .page-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Sections */
    section {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .service-card {
        padding: var(--spacing-xl);
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    .service-title {
        font-size: var(--font-size-xl);
    }
    
    /* Work Section */
    .work-image {
        height: 200px;
    }
    
    .work-content {
        padding: var(--spacing-md);
    }
    
    .work-title {
        font-size: var(--font-size-lg);
    }
    
    /* CTA Section */
    .cta-title {
        font-size: var(--font-size-2xl);
    }
    
    .cta-subtitle {
        font-size: var(--font-size-base);
        margin-bottom: var(--spacing-xl);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .cta-buttons .btn {
        width: 100%;
        max-width: 280px;
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-2xl) 0 var(--spacing-md);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .footer-brand {
        max-width: none;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    /* Cookie Banner */
    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }
    
    .cookie-buttons {
        justify-content: center;
        width: 100%;
    }
    
    .cookie-buttons .btn {
        flex: 1;
        min-width: 100px;
    }
    
    /* Forms */
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .contact-content {
        flex-direction: column;
        gap: var(--spacing-xl);
    }
    
    .contact-info {
        order: 2;
    }
    
    .contact-form-wrapper {
        order: 1;
    }
    
    /* Buttons */
    .btn-lg {
        padding: var(--spacing-md) var(--spacing-xl);
        font-size: var(--font-size-base);
    }
    
    /* Swiper customization for mobile */
    .swiper-button-next,
    .swiper-button-prev {
        display: none;
    }
    
    .swiper-pagination {
        bottom: 20px;
    }
}

/* ===== SMALL MOBILE STYLES (320px - 479px) ===== */
@media (max-width: 479px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
    }
    
    .page-title {
        font-size: var(--font-size-xl);
    }
    
    .service-card {
        padding: var(--spacing-lg);
    }
    
    .service-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .work-content {
        padding: var(--spacing-sm);
    }
    
    .cta-title {
        font-size: var(--font-size-xl);
    }
    
    .cookie-buttons {
        flex-direction: column;
    }
    
    .cookie-buttons .btn {
        width: 100%;
    }
}

/* ===== LANDSCAPE MOBILE STYLES ===== */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        height: 100vh;
        min-height: 400px;
    }
    
    .hero-slide {
        height: 100vh;
        min-height: 400px;
    }
    
    .hero-title {
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .hero-buttons {
        flex-direction: row;
        gap: var(--spacing-sm);
    }
    
    .hero-buttons .btn {
        width: auto;
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .navbar,
    .cookie-banner,
    .swiper-pagination,
    .swiper-button-next,
    .swiper-button-prev,
    .btn,
    .footer-social {
        display: none !important;
    }
    
    .hero {
        height: auto !important;
        min-height: auto !important;
    }
    
    .page-header {
        padding: var(--spacing-lg) 0 !important;
    }
    
    section {
        padding: var(--spacing-lg) 0 !important;
        page-break-inside: avoid;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    img {
        max-width: 100% !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
}
