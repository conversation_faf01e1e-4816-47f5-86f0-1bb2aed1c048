# Web Agency Website

A complete responsive website for a web agency specializing in website creation, management systems, advertising, and marketing services. Built with modern web technologies and optimized for performance and SEO.

## 🚀 Features

### Core Functionality
- **Fully Responsive Design** - Optimized for mobile, tablet, and desktop
- **Google Consent Mode v2** - GDPR compliant cookie banner
- **Google Analytics 4** - Complete tracking implementation
- **SEO Optimized** - Meta tags, structured data, and performance optimized
- **Accessibility** - WCAG compliant with proper focus management

### Pages & Sections
- **Homepage** - Hero slider, services showcase, portfolio carousel
- **About Us** - Team profiles, company story, values, statistics
- **Portfolio** - Filterable project grid with detailed modals
- **Contact** - Contact form, Google Maps integration, company info

### Technical Features
- **Modern JavaScript** - jQuery, GSAP animations, Alpine.js reactivity
- **Advanced CSS** - Custom properties, Grid/Flexbox, smooth animations
- **Performance** - Optimized images, lazy loading, efficient code
- **Cross-browser** - Compatible with all modern browsers

## 🛠 Technology Stack

### Frontend
- **HTML5** - Semantic markup with proper structure
- **CSS3** - Modern CSS with custom properties and Grid/Flexbox
- **JavaScript (ES6+)** - Modern JavaScript features

### Libraries & Frameworks
- **jQuery 3.7.1** - DOM manipulation and AJAX
- **GSAP 3.12.2** - High-performance animations
- **Alpine.js 3.x** - Lightweight reactive framework
- **Swiper.js 11** - Touch slider/carousel

### External Services
- **Google Analytics 4** - Web analytics and tracking
- **Google Maps API** - Interactive maps
- **Font Awesome 6.4.0** - Icon library
- **Google Fonts** - Inter font family

## 📁 Project Structure

```
/
├── index.html              # Homepage
├── about.html              # About Us page
├── portfolio.html          # Portfolio page
├── contact.html            # Contact page
├── README.md               # Project documentation
├── assets/
│   ├── css/
│   │   ├── main.css        # Main stylesheet
│   │   └── responsive.css  # Responsive breakpoints
│   ├── js/
│   │   ├── main.js         # Core functionality
│   │   ├── portfolio.js    # Portfolio filtering & modals
│   │   ├── contact.js      # Contact form & maps
│   │   └── analytics.js    # Google Analytics & consent
│   ├── images/
│   │   └── README.md       # Image requirements guide
│   └── documents/          # CV files and documents
```

## 🎨 Design System

### Color Palette
- **Ice Blue** (#e8f3f7) - Body background, light sections
- **Sky Blue** (#a3cce9) - Accent elements, borders
- **Dusty Blue** (#85abc4) - Secondary text, disabled states
- **Steel Blue** (#4b6281) - Footer background, secondary text
- **Navy Blue** (#22365b) - Primary text, navbar, primary buttons

### Typography
- **Font Family** - Inter (Google Fonts)
- **Font Weights** - 300, 400, 500, 600, 700
- **Responsive Scaling** - Fluid typography with CSS clamp()

### Breakpoints
- **Mobile** - 320px - 767px
- **Tablet** - 768px - 1023px
- **Desktop** - 1024px+

## 🚀 Getting Started

### Prerequisites
- Modern web browser
- Web server (for local development)
- Google Analytics 4 property (for tracking)
- Google Maps API key (for maps functionality)

### Installation

1. **Clone or download** the project files
2. **Set up a local web server** (required for proper functionality):
   ```bash
   # Using Python 3
   python -m http.server 8000
   
   # Using Node.js (http-server)
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Configure Google Services**:
   - Replace `G-XXXXXXXXXX` in `assets/js/analytics.js` with your GA4 Measurement ID
   - Replace `YOUR_API_KEY` in `contact.html` with your Google Maps API key

4. **Add Images**:
   - Follow the image requirements in `assets/images/README.md`
   - Add your actual images or use placeholder images for testing

### Configuration

#### Google Analytics Setup
1. Create a Google Analytics 4 property
2. Copy your Measurement ID
3. Update `GA_MEASUREMENT_ID` in `assets/js/analytics.js`

#### Google Maps Setup
1. Enable Google Maps JavaScript API in Google Cloud Console
2. Create an API key with Maps JavaScript API access
3. Replace `YOUR_API_KEY` in the Maps script tag in `contact.html`

#### Contact Form
The contact form currently uses a simulated submission. To implement real form submission:
1. Update the `sendFormData()` function in `assets/js/contact.js`
2. Point it to your backend endpoint or email service

## 📱 Responsive Design

The website is fully responsive with three main breakpoints:

### Mobile (320px - 767px)
- Stacked navigation menu
- Single column layouts
- Touch-optimized interactions
- Optimized image sizes

### Tablet (768px - 1023px)
- Collapsible navigation
- Two-column layouts where appropriate
- Balanced content distribution

### Desktop (1024px+)
- Full horizontal navigation
- Multi-column layouts
- Hover effects and animations
- Optimal content spacing

## 🔧 Customization

### Colors
Update CSS custom properties in `assets/css/main.css`:
```css
:root {
    --ice-blue: #e8f3f7;
    --sky-blue: #a3cce9;
    --dusty-blue: #85abc4;
    --steel-blue: #4b6281;
    --navy-blue: #22365b;
}
```

### Content
- Update company information in all HTML files
- Replace placeholder text with actual content
- Add real team member information
- Update contact details and address

### Images
- Follow the specifications in `assets/images/README.md`
- Optimize all images for web performance
- Use appropriate alt text for accessibility

## 🔍 SEO Features

### Meta Tags
- Comprehensive meta descriptions
- Open Graph tags for social sharing
- Twitter Card tags
- Proper title tags for each page

### Structured Data
- Organization schema markup
- Local business information
- Contact information markup

### Performance
- Optimized images and assets
- Efficient CSS and JavaScript
- Proper caching headers recommended
- Core Web Vitals optimized

## 📊 Analytics & Tracking

### Google Analytics 4
- Page view tracking
- Event tracking (form submissions, button clicks, etc.)
- Scroll depth tracking
- Time on page tracking
- Custom dimensions and metrics

### Google Consent Mode v2
- GDPR compliant cookie consent
- Granular consent management
- Analytics and marketing consent separation
- Consent state persistence

## 🛡 Privacy & Compliance

### GDPR Compliance
- Cookie consent banner
- Privacy policy integration
- Data processing transparency
- User consent management

### Accessibility
- WCAG 2.1 AA compliance
- Proper heading structure
- Alt text for images
- Keyboard navigation support
- Screen reader compatibility

## 🚀 Performance Optimization

### Core Web Vitals
- Optimized Largest Contentful Paint (LCP)
- Minimized Cumulative Layout Shift (CLS)
- Fast First Input Delay (FID)

### Best Practices
- Lazy loading for images
- Efficient CSS and JavaScript
- Proper resource hints
- Optimized font loading

## 📞 Support

For questions or support regarding this website template:

1. Check the documentation in this README
2. Review the image requirements guide
3. Ensure all API keys are properly configured
4. Test in multiple browsers and devices

## 📄 License

This project is provided as-is for educational and commercial use. Please ensure you have proper licenses for all external resources and APIs used.

---

**Built with ❤️ for modern web agencies**
