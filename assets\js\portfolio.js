// ===== PORTFOLIO JAVASCRIPT =====
// Portfolio filtering and modal functionality using Alpine.js

// Portfolio data
const portfolioProjects = [
    {
        id: 1,
        title: "E-commerce Fashion Store",
        shortDescription: "Piattaforma e-commerce completa per brand di moda con sistema di gestione ordini integrato.",
        fullDescription: "Sviluppo di una piattaforma e-commerce completa per un brand di moda emergente. Il progetto include un sistema di gestione ordini avanzato, integrazione con sistemi di pagamento multipli, gestione inventario in tempo reale e dashboard amministrativa completa. La piattaforma è stata ottimizzata per le conversioni e include funzionalità di wishlist, recensioni prodotti e sistema di raccomandazioni personalizzate.",
        category: "web",
        categoryName: "Sviluppo Web",
        image: "assets/images/project-1.jpg",
        link: "https://example-fashion-store.com",
        technologies: ["React", "Node.js", "MongoDB", "Stripe", "AWS"],
        client: "Fashion Brand XYZ",
        duration: "4 mesi",
        year: "2024",
        results: [
            "Aumento del 250% nelle vendite online",
            "Riduzione del 40% nel tasso di abbandono carrello",
            "Miglioramento del 60% nell'esperienza utente"
        ],
        gallery: [
            "assets/images/project-1-1.jpg",
            "assets/images/project-1-2.jpg",
            "assets/images/project-1-3.jpg"
        ]
    },
    {
        id: 2,
        title: "Sistema Gestionale Aziendale",
        shortDescription: "Applicazione web per la gestione completa di magazzino, clienti e fatturazione.",
        fullDescription: "Sviluppo di un sistema gestionale completo per un'azienda manifatturiera. Il sistema include moduli per la gestione del magazzino, CRM clienti, fatturazione elettronica, reportistica avanzata e dashboard executive. L'applicazione è stata progettata per essere scalabile e include API per l'integrazione con sistemi esterni.",
        category: "system",
        categoryName: "Sistema di Gestione",
        image: "assets/images/project-2.jpg",
        link: null,
        technologies: ["Vue.js", "Laravel", "MySQL", "Docker", "Redis"],
        client: "Manifattura ABC",
        duration: "6 mesi",
        year: "2023",
        results: [
            "Riduzione del 70% nei tempi di gestione ordini",
            "Automatizzazione del 90% dei processi manuali",
            "Miglioramento del 50% nella precisione inventario"
        ],
        gallery: [
            "assets/images/project-2-1.jpg",
            "assets/images/project-2-2.jpg",
            "assets/images/project-2-3.jpg"
        ]
    },
    {
        id: 3,
        title: "Campagna Marketing Digitale",
        shortDescription: "Strategia di marketing integrata che ha aumentato le conversioni del 300%.",
        fullDescription: "Sviluppo e implementazione di una strategia di marketing digitale completa per un'azienda B2B. Il progetto ha incluso audit SEO completo, ottimizzazione del sito web, creazione di contenuti, gestione social media, campagne Google Ads e email marketing automation. La strategia è stata basata su analisi approfondite del target e competitor analysis.",
        category: "marketing",
        categoryName: "Marketing Digitale",
        image: "assets/images/project-3.jpg",
        link: "https://example-marketing-case.com",
        technologies: ["Google Analytics", "Google Ads", "Facebook Ads", "HubSpot", "SEMrush"],
        client: "Tech Solutions Ltd",
        duration: "8 mesi",
        year: "2024",
        results: [
            "Aumento del 300% nelle conversioni",
            "Crescita del 180% nel traffico organico",
            "Miglioramento del 220% nel ROI pubblicitario"
        ],
        gallery: [
            "assets/images/project-3-1.jpg",
            "assets/images/project-3-2.jpg",
            "assets/images/project-3-3.jpg"
        ]
    },
    {
        id: 4,
        title: "App Mobile Fitness",
        shortDescription: "Applicazione mobile per il fitness con tracking allenamenti e piani personalizzati.",
        fullDescription: "Sviluppo di un'applicazione mobile completa per il fitness che include tracking degli allenamenti, piani di allenamento personalizzati, integrazione con dispositivi wearable, social features e sistema di gamification. L'app include anche un marketplace per personal trainer e nutrizionisti.",
        category: "web",
        categoryName: "Sviluppo Web",
        image: "assets/images/project-4.jpg",
        link: "https://example-fitness-app.com",
        technologies: ["React Native", "Firebase", "Node.js", "PostgreSQL", "Stripe"],
        client: "FitLife Startup",
        duration: "5 mesi",
        year: "2023",
        results: [
            "50.000+ download nei primi 3 mesi",
            "Rating 4.8/5 negli app store",
            "Retention rate del 75% dopo 30 giorni"
        ],
        gallery: [
            "assets/images/project-4-1.jpg",
            "assets/images/project-4-2.jpg",
            "assets/images/project-4-3.jpg"
        ]
    },
    {
        id: 5,
        title: "Campagna Google Ads B2B",
        shortDescription: "Campagna pubblicitaria Google Ads per azienda B2B con ROI del 400%.",
        fullDescription: "Gestione completa di campagne Google Ads per un'azienda di software B2B. Il progetto ha incluso ricerca keyword approfondita, creazione di landing pages ottimizzate, setup di conversion tracking avanzato, A/B testing continuo e ottimizzazione basata sui dati. Le campagne hanno coperto Search, Display e YouTube.",
        category: "advertising",
        categoryName: "Pubblicità Online",
        image: "assets/images/project-5.jpg",
        link: null,
        technologies: ["Google Ads", "Google Analytics", "Unbounce", "Hotjar", "Zapier"],
        client: "SoftwareCorp",
        duration: "12 mesi",
        year: "2024",
        results: [
            "ROI del 400% sugli investimenti pubblicitari",
            "Riduzione del 60% nel costo per acquisizione",
            "Aumento del 250% nei lead qualificati"
        ],
        gallery: [
            "assets/images/project-5-1.jpg",
            "assets/images/project-5-2.jpg",
            "assets/images/project-5-3.jpg"
        ]
    },
    {
        id: 6,
        title: "Piattaforma E-learning",
        shortDescription: "Piattaforma di formazione online con sistema di certificazioni e gamification.",
        fullDescription: "Sviluppo di una piattaforma e-learning completa per un'azienda di formazione professionale. La piattaforma include gestione corsi, sistema di certificazioni, gamification, live streaming, forum di discussione e analytics avanzati per il tracking del progresso degli studenti.",
        category: "system",
        categoryName: "Sistema di Gestione",
        image: "assets/images/project-6.jpg",
        link: "https://example-elearning.com",
        technologies: ["Angular", "Django", "PostgreSQL", "Redis", "WebRTC"],
        client: "EduTech Academy",
        duration: "7 mesi",
        year: "2023",
        results: [
            "10.000+ studenti attivi",
            "Tasso di completamento corsi del 85%",
            "Soddisfazione utenti del 92%"
        ],
        gallery: [
            "assets/images/project-6-1.jpg",
            "assets/images/project-6-2.jpg",
            "assets/images/project-6-3.jpg"
        ]
    }
];

// Alpine.js component for portfolio filtering
function portfolioFilter() {
    return {
        activeFilter: 'all',
        projects: portfolioProjects,
        
        get filteredProjects() {
            if (this.activeFilter === 'all') {
                return this.projects;
            }
            return this.projects.filter(project => project.category === this.activeFilter);
        },
        
        filterProjects(category) {
            this.activeFilter = category;
            
            // Track filter usage
            if (typeof trackEvent === 'function') {
                trackEvent('portfolio_filter', 'filter_change', category);
            }
            
            // Animate filtered results
            this.$nextTick(() => {
                gsap.fromTo('.project-card', 
                    { opacity: 0, y: 20 },
                    { 
                        opacity: 1, 
                        y: 0, 
                        duration: 0.5, 
                        stagger: 0.1,
                        ease: 'power2.out'
                    }
                );
            });
        },
        
        openModal(project) {
            this.showProjectModal(project);
            
            // Track modal open
            if (typeof trackEvent === 'function') {
                trackEvent('portfolio_modal', 'open', project.title);
            }
        },
        
        showProjectModal(project) {
            const modal = document.getElementById('project-modal');
            const modalBody = document.getElementById('modal-body');
            
            // Create modal content
            const modalContent = `
                <div class="project-modal-content">
                    <div class="project-modal-header">
                        <div class="project-modal-image">
                            <img src="${project.image}" alt="${project.title}" class="img-fluid">
                        </div>
                        <div class="project-modal-info">
                            <div class="project-category">${project.categoryName}</div>
                            <h2 class="project-modal-title">${project.title}</h2>
                            <p class="project-modal-description">${project.fullDescription}</p>
                            ${project.link ? `<a href="${project.link}" target="_blank" class="btn btn-primary">Visita il sito <i class="fas fa-external-link-alt"></i></a>` : ''}
                        </div>
                    </div>
                    
                    <div class="project-modal-details">
                        <div class="project-detail-section">
                            <h4>Dettagli Progetto</h4>
                            <div class="project-details-grid">
                                <div class="detail-item">
                                    <strong>Cliente:</strong> ${project.client}
                                </div>
                                <div class="detail-item">
                                    <strong>Durata:</strong> ${project.duration}
                                </div>
                                <div class="detail-item">
                                    <strong>Anno:</strong> ${project.year}
                                </div>
                            </div>
                        </div>
                        
                        <div class="project-detail-section">
                            <h4>Tecnologie Utilizzate</h4>
                            <div class="technologies-list">
                                ${project.technologies.map(tech => `<span class="tech-tag">${tech}</span>`).join('')}
                            </div>
                        </div>
                        
                        <div class="project-detail-section">
                            <h4>Risultati Ottenuti</h4>
                            <ul class="results-list">
                                ${project.results.map(result => `<li>${result}</li>`).join('')}
                            </ul>
                        </div>
                        
                        <div class="project-detail-section">
                            <h4>Gallery</h4>
                            <div class="project-gallery">
                                ${project.gallery.map(img => `
                                    <div class="gallery-item">
                                        <img src="${img}" alt="${project.title} gallery" class="img-fluid">
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            modalBody.innerHTML = modalContent;
            
            // Show modal with animation
            gsap.set(modal, { display: 'flex', opacity: 0 });
            gsap.to(modal, { opacity: 1, duration: 0.3 });
            gsap.fromTo('.modal-content', 
                { scale: 0.8, opacity: 0 },
                { scale: 1, opacity: 1, duration: 0.3, ease: 'back.out(1.7)' }
            );
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }
}

// Initialize portfolio when DOM is ready
$(document).ready(function() {
    // Close modal functionality
    $(document).on('click', '.modal-backdrop, .modal-close', function() {
        closeProjectModal();
    });
    
    // Close modal on escape key
    $(document).keyup(function(e) {
        if (e.keyCode === 27) { // Escape key
            closeProjectModal();
        }
    });
    
    // Prevent modal close when clicking inside modal content
    $(document).on('click', '.modal-body', function(e) {
        e.stopPropagation();
    });
});

function closeProjectModal() {
    const modal = document.getElementById('project-modal');
    
    gsap.to(modal, { 
        opacity: 0, 
        duration: 0.3,
        onComplete: () => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
    
    // Track modal close
    if (typeof trackEvent === 'function') {
        trackEvent('portfolio_modal', 'close');
    }
}
