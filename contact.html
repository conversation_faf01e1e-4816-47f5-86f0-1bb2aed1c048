<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Contatta Web Agency per il tuo progetto digitale. Richiedi un preventivo gratuito per sviluppo web, marketing digitale e sistemi di gestione.">
    <meta name="keywords" content="contatti web agency, preventivo sito web, consulenza marketing digitale, sviluppo web milano">
    <meta name="author" content="Web Agency">
    <meta name="robots" content="index, follow">
    
    <title>Contatti - Web Agency</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- External Libraries CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Cookie Banner -->
    <div id="cookie-banner" class="cookie-banner" style="display: none;">
        <div class="cookie-content">
            <div class="cookie-text">
                <h4>Utilizziamo i cookie</h4>
                <p>Questo sito utilizza cookie per migliorare l'esperienza utente e per analizzare il traffico. Continuando a navigare accetti l'uso dei cookie.</p>
            </div>
            <div class="cookie-buttons">
                <button id="cookie-accept" class="btn btn-primary">Accetta tutti</button>
                <button id="cookie-settings" class="btn btn-secondary">Impostazioni</button>
                <button id="cookie-reject" class="btn btn-outline">Rifiuta</button>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="index.html">
                    <img src="assets/images/logo.png" alt="Web Agency Logo" class="logo">
                </a>
            </div>
            <div class="nav-menu" id="nav-menu">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link">Chi Siamo</a>
                    </li>
                    <li class="nav-item">
                        <a href="portfolio.html" class="nav-link">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link active">Contatti</a>
                    </li>
                </ul>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title">Contatti</h1>
                <p class="page-subtitle">Iniziamo insieme il tuo prossimo progetto digitale</p>
                <nav class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current">Contatti</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-content">
                <div class="contact-info">
                    <h2 class="section-title">Parliamo del tuo progetto</h2>
                    <p class="section-description">
                        Siamo qui per aiutarti a trasformare le tue idee in realtà digitale. 
                        Contattaci per una consulenza gratuita e scopri come possiamo far crescere il tuo business online.
                    </p>
                    
                    <div class="contact-details">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Indirizzo</h4>
                                <p>Via Roma 123<br>20121 Milano, Italia</p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Telefono</h4>
                                <p><a href="tel:+390212345678">+39 02 1234567</a></p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Email</h4>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>
                        
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-text">
                                <h4>Orari</h4>
                                <p>Lun - Ven: 9:00 - 18:00<br>Sab: 9:00 - 13:00</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="social-links">
                        <h4>Seguici sui social</h4>
                        <div class="social-icons">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        </div>
                    </div>
                </div>
                
                <div class="contact-form-wrapper">
                    <form class="contact-form" id="contact-form" x-data="contactForm()">
                        <h3 class="form-title">Richiedi un preventivo gratuito</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Nome *</label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    x-model="form.name"
                                    :class="{ 'error': errors.name }"
                                    required
                                >
                                <span class="error-message" x-show="errors.name" x-text="errors.name"></span>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    x-model="form.email"
                                    :class="{ 'error': errors.email }"
                                    required
                                >
                                <span class="error-message" x-show="errors.email" x-text="errors.email"></span>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">Telefono</label>
                                <input 
                                    type="tel" 
                                    id="phone" 
                                    name="phone" 
                                    x-model="form.phone"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="company">Azienda</label>
                                <input 
                                    type="text" 
                                    id="company" 
                                    name="company" 
                                    x-model="form.company"
                                >
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="service">Servizio di interesse *</label>
                            <select 
                                id="service" 
                                name="service" 
                                x-model="form.service"
                                :class="{ 'error': errors.service }"
                                required
                            >
                                <option value="">Seleziona un servizio</option>
                                <option value="web-development">Sviluppo Web</option>
                                <option value="management-system">Sistema di Gestione</option>
                                <option value="digital-marketing">Marketing Digitale</option>
                                <option value="online-advertising">Pubblicità Online</option>
                                <option value="consultation">Consulenza</option>
                                <option value="other">Altro</option>
                            </select>
                            <span class="error-message" x-show="errors.service" x-text="errors.service"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="budget">Budget indicativo</label>
                            <select id="budget" name="budget" x-model="form.budget">
                                <option value="">Seleziona un range</option>
                                <option value="under-5k">Meno di €5.000</option>
                                <option value="5k-10k">€5.000 - €10.000</option>
                                <option value="10k-25k">€10.000 - €25.000</option>
                                <option value="25k-50k">€25.000 - €50.000</option>
                                <option value="over-50k">Oltre €50.000</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Messaggio *</label>
                            <textarea 
                                id="message" 
                                name="message" 
                                rows="5" 
                                x-model="form.message"
                                :class="{ 'error': errors.message }"
                                placeholder="Descrivi il tuo progetto e i tuoi obiettivi..."
                                required
                            ></textarea>
                            <span class="error-message" x-show="errors.message" x-text="errors.message"></span>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input 
                                    type="checkbox" 
                                    name="privacy" 
                                    x-model="form.privacy"
                                    :class="{ 'error': errors.privacy }"
                                    required
                                >
                                <span class="checkmark"></span>
                                Accetto la <a href="#privacy" target="_blank">Privacy Policy</a> *
                            </label>
                            <span class="error-message" x-show="errors.privacy" x-text="errors.privacy"></span>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input 
                                    type="checkbox" 
                                    name="newsletter" 
                                    x-model="form.newsletter"
                                >
                                <span class="checkmark"></span>
                                Desidero ricevere aggiornamenti e offerte via email
                            </label>
                        </div>
                        
                        <button 
                            type="submit" 
                            class="btn btn-primary btn-lg btn-full"
                            :disabled="isSubmitting"
                            @click.prevent="submitForm()"
                        >
                            <span x-show="!isSubmitting">Invia richiesta</span>
                            <span x-show="isSubmitting">
                                <i class="fas fa-spinner fa-spin"></i> Invio in corso...
                            </span>
                        </button>
                        
                        <div class="form-message" x-show="message.text" :class="message.type">
                            <span x-text="message.text"></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
        <div class="container">
            <h2 class="section-title text-center">Dove siamo</h2>
        </div>
        <div class="map-container">
            <div id="google-map" class="google-map"></div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/images/logo-white.png" alt="Web Agency Logo" class="footer-logo">
                        <p class="footer-description">Agenzia web specializzata nella creazione di soluzioni digitali innovative per far crescere il tuo business online.</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Servizi</h4>
                    <ul class="footer-links">
                        <li><a href="index.html#services">Sviluppo Web</a></li>
                        <li><a href="index.html#services">Sistemi di Gestione</a></li>
                        <li><a href="index.html#services">Marketing Digitale</a></li>
                        <li><a href="index.html#services">Pubblicità Online</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Azienda</h4>
                    <ul class="footer-links">
                        <li><a href="about.html">Chi Siamo</a></li>
                        <li><a href="portfolio.html">Portfolio</a></li>
                        <li><a href="contact.html">Contatti</a></li>
                        <li><a href="#privacy">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Contatti</h4>
                    <div class="footer-contact">
                        <p><i class="fas fa-map-marker-alt"></i> Via Roma 123, 20121 Milano</p>
                        <p><i class="fas fa-phone"></i> +39 02 1234567</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                    <div class="footer-social">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Web Agency. Tutti i diritti riservati.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/contact.js"></script>
    <script src="assets/js/analytics.js"></script>
    
    <!-- Google Maps API -->
    <script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap"></script>
</body>
</html>
