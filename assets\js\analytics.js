// ===== GOOGLE ANALYTICS & CONSENT MANAGEMENT =====
// Google Analytics 4 implementation with Google Consent Mode v2

// Configuration
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX'; // Replace with your actual GA4 Measurement ID
const GTM_ID = 'GTM-XXXXXXX'; // Replace with your actual GTM ID (optional)

// Initialize Google Analytics with Consent Mode
function initAnalytics() {
    // Check if user has consented to analytics
    const analyticsConsent = getCookie('analyticsConsent');
    const marketingConsent = getCookie('marketingConsent');
    
    if (analyticsConsent === 'true') {
        loadGoogleAnalytics();
    }
    
    // Set up consent mode
    setupConsentMode(analyticsConsent, marketingConsent);
}

// Load Google Analytics script
function loadGoogleAnalytics() {
    // Load gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);
    
    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    window.gtag = gtag;
    
    gtag('js', new Date());
    gtag('config', GA_MEASUREMENT_ID, {
        // Enhanced measurement events
        enhanced_measurements: {
            scrolls: true,
            outbound_clicks: true,
            site_search: true,
            video_engagement: true,
            file_downloads: true
        },
        // Custom parameters
        custom_map: {
            'custom_parameter_1': 'page_type',
            'custom_parameter_2': 'user_type'
        },
        // Cookie settings
        cookie_flags: 'SameSite=None;Secure',
        // Privacy settings
        anonymize_ip: true,
        allow_google_signals: false
    });
    
    // Track initial page view
    trackPageView();
}

// Setup Google Consent Mode v2
function setupConsentMode(analyticsConsent, marketingConsent) {
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    window.gtag = gtag;
    
    // Set default consent state
    gtag('consent', 'default', {
        'analytics_storage': analyticsConsent === 'true' ? 'granted' : 'denied',
        'ad_storage': marketingConsent === 'true' ? 'granted' : 'denied',
        'ad_user_data': marketingConsent === 'true' ? 'granted' : 'denied',
        'ad_personalization': marketingConsent === 'true' ? 'granted' : 'denied',
        'functionality_storage': 'granted',
        'security_storage': 'granted'
    });
    
    // Update consent when user makes a choice
    updateConsentMode();
}

// Update consent mode based on user choice
function updateConsentMode() {
    const analyticsConsent = getCookie('analyticsConsent');
    const marketingConsent = getCookie('marketingConsent');
    
    if (typeof gtag !== 'undefined') {
        gtag('consent', 'update', {
            'analytics_storage': analyticsConsent === 'true' ? 'granted' : 'denied',
            'ad_storage': marketingConsent === 'true' ? 'granted' : 'denied',
            'ad_user_data': marketingConsent === 'true' ? 'granted' : 'denied',
            'ad_personalization': marketingConsent === 'true' ? 'granted' : 'denied'
        });
    }
}

// Track page views
function trackPageView(pageTitle = document.title, pagePath = window.location.pathname) {
    if (typeof gtag !== 'undefined' && getCookie('analyticsConsent') === 'true') {
        gtag('event', 'page_view', {
            page_title: pageTitle,
            page_location: window.location.href,
            page_path: pagePath,
            content_group1: getPageType(),
            content_group2: getUserType()
        });
    }
}

// Track custom events
function trackEvent(eventName, eventAction, eventLabel = '', eventValue = null) {
    if (typeof gtag !== 'undefined' && getCookie('analyticsConsent') === 'true') {
        const eventParams = {
            event_category: eventName,
            event_label: eventLabel
        };
        
        if (eventValue !== null) {
            eventParams.value = eventValue;
        }
        
        gtag('event', eventAction, eventParams);
    }
}

// Track scroll depth
function trackScrollDepth() {
    let maxScroll = 0;
    const milestones = [25, 50, 75, 90, 100];
    const tracked = [];
    
    $(window).scroll(function() {
        const scrollTop = $(window).scrollTop();
        const docHeight = $(document).height() - $(window).height();
        const scrollPercent = Math.round((scrollTop / docHeight) * 100);
        
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            
            milestones.forEach(milestone => {
                if (scrollPercent >= milestone && !tracked.includes(milestone)) {
                    tracked.push(milestone);
                    trackEvent('scroll_depth', 'scroll', `${milestone}%`, milestone);
                }
            });
        }
    });
}

// Track time on page
function trackTimeOnPage() {
    const startTime = Date.now();
    const intervals = [30, 60, 120, 300, 600]; // seconds
    const tracked = [];
    
    intervals.forEach(interval => {
        setTimeout(() => {
            if (!tracked.includes(interval)) {
                tracked.push(interval);
                trackEvent('engagement', 'time_on_page', `${interval}s`, interval);
            }
        }, interval * 1000);
    });
    
    // Track time on page when user leaves
    $(window).on('beforeunload', function() {
        const timeSpent = Math.round((Date.now() - startTime) / 1000);
        trackEvent('engagement', 'session_duration', 'page_exit', timeSpent);
    });
}

// Track form interactions
function trackFormInteractions() {
    // Track form starts
    $('form').on('focusin', 'input, select, textarea', function() {
        const formId = $(this).closest('form').attr('id') || 'unknown_form';
        trackEvent('form_interaction', 'form_start', formId);
    });
    
    // Track form submissions
    $('form').on('submit', function() {
        const formId = $(this).attr('id') || 'unknown_form';
        trackEvent('form_interaction', 'form_submit', formId);
    });
    
    // Track form abandonment
    let formStarted = false;
    $('form').on('focusin', 'input, select, textarea', function() {
        formStarted = true;
    });
    
    $(window).on('beforeunload', function() {
        if (formStarted) {
            const activeForm = $('form:has(input:focus, select:focus, textarea:focus)');
            if (activeForm.length) {
                const formId = activeForm.attr('id') || 'unknown_form';
                trackEvent('form_interaction', 'form_abandon', formId);
            }
        }
    });
}

// Track button clicks
function trackButtonClicks() {
    $('button, .btn, a[href^="tel:"], a[href^="mailto:"]').click(function() {
        const buttonText = $(this).text().trim() || $(this).attr('aria-label') || 'unknown';
        const buttonType = this.tagName.toLowerCase();
        const href = $(this).attr('href');
        
        if (href && href.startsWith('tel:')) {
            trackEvent('contact', 'phone_click', buttonText);
        } else if (href && href.startsWith('mailto:')) {
            trackEvent('contact', 'email_click', buttonText);
        } else {
            trackEvent('button_click', buttonType, buttonText);
        }
    });
}

// Track external link clicks
function trackExternalLinks() {
    $('a[href^="http"]').not('[href*="' + window.location.hostname + '"]').click(function() {
        const url = $(this).attr('href');
        const linkText = $(this).text().trim() || 'external_link';
        trackEvent('external_link', 'click', url);
    });
}

// Track file downloads
function trackFileDownloads() {
    const fileExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'gif'];
    
    fileExtensions.forEach(ext => {
        $(`a[href$=".${ext}"]`).click(function() {
            const fileName = $(this).attr('href').split('/').pop();
            trackEvent('file_download', 'download', fileName);
        });
    });
}

// Track video interactions
function trackVideoInteractions() {
    $('video').each(function() {
        const video = this;
        const videoSrc = $(video).attr('src') || $(video).find('source').first().attr('src') || 'unknown';
        
        video.addEventListener('play', () => {
            trackEvent('video', 'play', videoSrc);
        });
        
        video.addEventListener('pause', () => {
            trackEvent('video', 'pause', videoSrc);
        });
        
        video.addEventListener('ended', () => {
            trackEvent('video', 'complete', videoSrc);
        });
    });
}

// Track search interactions
function trackSearchInteractions() {
    $('input[type="search"], input[name*="search"], input[id*="search"]').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            const searchTerm = $(this).val().trim();
            if (searchTerm) {
                trackEvent('site_search', 'search', searchTerm);
            }
        }
    });
}

// Helper functions
function getPageType() {
    const path = window.location.pathname;
    if (path === '/' || path === '/index.html') return 'homepage';
    if (path.includes('about')) return 'about';
    if (path.includes('portfolio')) return 'portfolio';
    if (path.includes('contact')) return 'contact';
    return 'other';
}

function getUserType() {
    // This could be enhanced with actual user data
    const isReturning = getCookie('returning_visitor');
    return isReturning ? 'returning' : 'new';
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

// Initialize analytics when DOM is ready
$(document).ready(function() {
    // Set returning visitor cookie
    if (!getCookie('returning_visitor')) {
        document.cookie = 'returning_visitor=true; expires=' + new Date(Date.now() + 30*24*60*60*1000).toUTCString() + '; path=/; SameSite=Lax';
    }
    
    // Initialize tracking functions
    trackScrollDepth();
    trackTimeOnPage();
    trackFormInteractions();
    trackButtonClicks();
    trackExternalLinks();
    trackFileDownloads();
    trackVideoInteractions();
    trackSearchInteractions();
    
    // Initialize analytics if consent is already given
    if (getCookie('analyticsConsent') === 'true') {
        initAnalytics();
    }
});

// Export functions for global use
window.initAnalytics = initAnalytics;
window.trackEvent = trackEvent;
window.trackPageView = trackPageView;
window.updateConsentMode = updateConsentMode;
