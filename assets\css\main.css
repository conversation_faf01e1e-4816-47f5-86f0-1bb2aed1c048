/* ===== CSS VARIABLES ===== */
:root {
    /* Color Palette */
    --ice-blue: #e8f3f7;
    --sky-blue: #a3cce9;
    --dusty-blue: #85abc4;
    --steel-blue: #4b6281;
    --navy-blue: #22365b;
    
    /* Additional Colors */
    --white: #ffffff;
    --black: #000000;
    --gray-light: #f8f9fa;
    --gray-medium: #6c757d;
    --gray-dark: #343a40;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 40px;
    --font-size-5xl: 48px;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    
    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: var(--font-size-base);
}

body {
    font-family: var(--font-family);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--navy-blue);
    background-color: var(--ice-blue);
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
    color: var(--navy-blue);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--steel-blue);
}

a {
    color: var(--navy-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--sky-blue);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.img-fluid {
    max-width: 100%;
    height: auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    text-align: center;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    gap: var(--spacing-sm);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(163, 204, 233, 0.3);
}

.btn-primary {
    background-color: var(--navy-blue);
    color: var(--white);
    border-color: var(--navy-blue);
}

.btn-primary:hover {
    background-color: var(--steel-blue);
    border-color: var(--steel-blue);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--sky-blue);
    color: var(--navy-blue);
    border-color: var(--sky-blue);
}

.btn-secondary:hover {
    background-color: var(--dusty-blue);
    border-color: var(--dusty-blue);
    color: var(--white);
}

.btn-outline {
    background-color: transparent;
    color: var(--navy-blue);
    border-color: var(--navy-blue);
}

.btn-outline:hover {
    background-color: var(--navy-blue);
    color: var(--white);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-2xl);
    font-size: var(--font-size-lg);
}

.btn-full {
    width: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--navy-blue);
    backdrop-filter: blur(10px);
    z-index: var(--z-fixed);
    padding: var(--spacing-md) 0;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background-color: rgba(34, 54, 91, 0.95);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-sm) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--white);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) 0;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--sky-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--sky-blue);
    transition: width var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle .bar {
    width: 25px;
    height: 3px;
    background-color: var(--white);
    transition: all var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
}

.hero-slider {
    height: 100%;
}

.hero-slide {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 54, 91, 0.8) 0%, rgba(75, 98, 129, 0.6) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--white);
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    color: var(--white);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    color: var(--ice-blue);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.4s both;
}

/* ===== SECTIONS ===== */
section {
    padding: var(--spacing-4xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
    color: var(--navy-blue);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--steel-blue);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SERVICES SECTION ===== */
.services {
    background-color: var(--white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
}

.service-card {
    background-color: var(--white);
    border: 2px solid var(--sky-blue);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    box-shadow: 0 4px 6px rgba(34, 54, 91, 0.1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(34, 54, 91, 0.15);
    border-color: var(--navy-blue);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--sky-blue), var(--navy-blue));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.service-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--navy-blue);
}

.service-description {
    color: var(--steel-blue);
    line-height: var(--line-height-relaxed);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.fade-out {
    animation: fadeIn 0.6s ease-out reverse;
}

/* ===== SWIPER CUSTOMIZATION ===== */
.swiper-pagination-bullet {
    background-color: var(--white);
    opacity: 0.5;
    width: 12px;
    height: 12px;
}

.swiper-pagination-bullet-active {
    background-color: var(--sky-blue);
    opacity: 1;
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--white);
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-full);
    width: 50px;
    height: 50px;
    margin-top: -25px;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.swiper-button-next::after,
.swiper-button-prev::after {
    font-size: 18px;
}

/* ===== OUR WORK SECTION ===== */
.our-work {
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
}

.work-slider .swiper-slide {
    height: auto;
}

.work-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    height: 100%;
}

.work-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.work-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.work-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.work-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 54, 91, 0.8) 0%, rgba(75, 98, 129, 0.6) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.work-card:hover .work-overlay {
    opacity: 1;
}

.work-card:hover .work-image img {
    transform: scale(1.1);
}

.work-link {
    width: 50px;
    height: 50px;
    background-color: var(--white);
    color: var(--navy-blue);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.work-link:hover {
    background-color: var(--sky-blue);
    color: var(--white);
    transform: scale(1.1);
}

.work-content {
    padding: var(--spacing-lg);
}

.work-category {
    font-size: var(--font-size-sm);
    color: var(--sky-blue);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
}

.work-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--navy-blue);
}

.work-description {
    color: var(--steel-blue);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

/* ===== CTA SECTION ===== */
.cta {
    background: linear-gradient(135deg, var(--navy-blue) 0%, var(--steel-blue) 100%);
    color: var(--white);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.cta-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2xl);
    color: var(--ice-blue);
    line-height: var(--line-height-relaxed);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.cta .btn-outline {
    border-color: var(--white);
    color: var(--white);
}

.cta .btn-outline:hover {
    background-color: var(--white);
    color: var(--navy-blue);
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--steel-blue);
    color: var(--ice-blue);
    padding: var(--spacing-4xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    height: 40px;
    margin-bottom: var(--spacing-lg);
}

.footer-description {
    color: var(--ice-blue);
    line-height: var(--line-height-relaxed);
}

.footer-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: var(--spacing-lg);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--ice-blue);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--sky-blue);
}

.footer-contact p {
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-contact i {
    width: 16px;
    color: var(--sky-blue);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--ice-blue);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--sky-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
    color: var(--ice-blue);
}

/* ===== PAGE HEADER ===== */
.page-header {
    background: linear-gradient(135deg, var(--navy-blue) 0%, var(--steel-blue) 100%);
    color: var(--white);
    text-align: center;
    padding: calc(80px + var(--spacing-4xl)) 0 var(--spacing-4xl);
    margin-top: 0;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    color: var(--white);
}

.page-subtitle {
    font-size: var(--font-size-xl);
    color: var(--ice-blue);
    margin-bottom: var(--spacing-xl);
    line-height: var(--line-height-relaxed);
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.breadcrumb a {
    color: var(--ice-blue);
    transition: color var(--transition-fast);
}

.breadcrumb a:hover {
    color: var(--sky-blue);
}

.breadcrumb-separator {
    color: var(--dusty-blue);
}

.breadcrumb-current {
    color: var(--white);
    font-weight: var(--font-weight-medium);
}

/* ===== COOKIE BANNER ===== */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    border-top: 3px solid var(--sky-blue);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-modal);
    padding: var(--spacing-lg);
    animation: slideInUp 0.5s ease-out;
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.cookie-text h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--navy-blue);
}

.cookie-text p {
    font-size: var(--font-size-sm);
    color: var(--steel-blue);
    margin: 0;
    max-width: 600px;
}

.cookie-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--navy-blue);
    margin-bottom: var(--spacing-sm);
}

input[type="text"],
input[type="email"],
input[type="tel"],
select,
textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--sky-blue);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    background-color: var(--white);
    color: var(--navy-blue);
    transition: all var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--navy-blue);
    box-shadow: 0 0 0 3px rgba(163, 204, 233, 0.3);
}

input.error,
select.error,
textarea.error {
    border-color: var(--danger);
}

.error-message {
    color: var(--danger);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
    display: block;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--sky-blue);
    border-radius: var(--border-radius-sm);
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--navy-blue);
    border-color: var(--navy-blue);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid var(--white);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.form-message {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-lg);
    font-weight: var(--font-weight-medium);
}

.form-message.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid var(--success);
}

.form-message.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border: 1px solid var(--danger);
}

/* ===== ABOUT PAGE STYLES ===== */
.company-story {
    background-color: var(--white);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.story-description {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

.story-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--sky-blue);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--navy-blue);
    display: block;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--steel-blue);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.story-image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Values Section */
.values {
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
}

.value-card {
    background-color: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--sky-blue);
    transition: all var(--transition-normal);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--navy-blue);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--sky-blue), var(--navy-blue));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.value-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--navy-blue);
}

.value-description {
    color: var(--steel-blue);
    line-height: var(--line-height-relaxed);
}

/* Team Section */
.team {
    background-color: var(--white);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
}

.team-member {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--sky-blue);
    transition: all var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--navy-blue);
}

.member-image {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 54, 91, 0.8) 0%, rgba(75, 98, 129, 0.6) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-social {
    display: flex;
    gap: var(--spacing-md);
}

.member-social .social-link {
    width: 45px;
    height: 45px;
    background-color: var(--white);
    color: var(--navy-blue);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.member-social .social-link:hover {
    background-color: var(--sky-blue);
    color: var(--white);
    transform: scale(1.1);
}

.member-info {
    padding: var(--spacing-lg);
    text-align: center;
}

.member-name {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--navy-blue);
}

.member-role {
    font-size: var(--font-size-base);
    color: var(--sky-blue);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.member-description {
    color: var(--steel-blue);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

/* ===== PORTFOLIO PAGE STYLES ===== */
.portfolio-filter {
    background-color: var(--white);
    padding: var(--spacing-2xl) 0;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: transparent;
    color: var(--steel-blue);
    border: 2px solid var(--sky-blue);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover,
.filter-btn.active {
    background-color: var(--navy-blue);
    color: var(--white);
    border-color: var(--navy-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.portfolio-grid {
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
    padding: var(--spacing-4xl) 0;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.project-card {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 2px solid var(--sky-blue);
    transition: all var(--transition-normal);
    height: 100%;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--navy-blue);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(34, 54, 91, 0.9) 0%, rgba(75, 98, 129, 0.7) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

.project-actions {
    display: flex;
    gap: var(--spacing-md);
}

.project-btn {
    width: 50px;
    height: 50px;
    background-color: var(--white);
    color: var(--navy-blue);
    border: none;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
}

.project-btn:hover {
    background-color: var(--sky-blue);
    color: var(--white);
    transform: scale(1.1);
}

.project-content {
    padding: var(--spacing-lg);
}

.project-category {
    font-size: var(--font-size-sm);
    color: var(--sky-blue);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-sm);
}

.project-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--navy-blue);
}

.project-description {
    color: var(--steel-blue);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    padding: var(--spacing-lg);
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow: var(--shadow-xl);
}

.modal-close {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 40px;
    height: 40px;
    background-color: var(--navy-blue);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--steel-blue);
    transform: scale(1.1);
}

.modal-body {
    padding: var(--spacing-2xl);
}

.project-modal-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.project-modal-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.project-modal-image img {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
}

.project-modal-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--navy-blue);
}

.project-modal-description {
    color: var(--steel-blue);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

.project-modal-details {
    display: grid;
    gap: var(--spacing-2xl);
}

.project-detail-section h4 {
    color: var(--navy-blue);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.project-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.detail-item {
    padding: var(--spacing-md);
    background-color: var(--ice-blue);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--sky-blue);
}

.technologies-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.tech-tag {
    background-color: var(--navy-blue);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.results-list {
    list-style: none;
    padding: 0;
}

.results-list li {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--sky-blue);
    position: relative;
    padding-left: var(--spacing-lg);
}

.results-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success);
    font-weight: var(--font-weight-bold);
}

.project-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.gallery-item img {
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-fast);
}

.gallery-item img:hover {
    transform: scale(1.05);
}

/* ===== CONTACT PAGE STYLES ===== */
.contact-section {
    background-color: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: start;
}

.contact-info {
    padding-right: var(--spacing-lg);
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--steel-blue);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-2xl);
}

.contact-details {
    margin-bottom: var(--spacing-2xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--sky-blue);
    transition: all var(--transition-fast);
}

.contact-item:hover {
    transform: translateX(5px);
    border-left-color: var(--navy-blue);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--sky-blue), var(--navy-blue));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.contact-text h4 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--navy-blue);
}

.contact-text p {
    color: var(--steel-blue);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.contact-text a {
    color: var(--navy-blue);
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.contact-text a:hover {
    color: var(--sky-blue);
}

.social-links h4 {
    color: var(--navy-blue);
    margin-bottom: var(--spacing-md);
}

.social-icons {
    display: flex;
    gap: var(--spacing-md);
}

.social-icons .social-link {
    width: 45px;
    height: 45px;
    background-color: var(--sky-blue);
    color: var(--white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.social-icons .social-link:hover {
    background-color: var(--navy-blue);
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.contact-form-wrapper {
    background: linear-gradient(135deg, rgba(163, 204, 233, 0.1) 0%, rgba(240, 248, 252, 1) 100%);
    padding: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--sky-blue);
    box-shadow: var(--shadow-lg);
}

.form-title {
    font-size: var(--font-size-2xl);
    color: var(--navy-blue);
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

/* Map Section */
.map-section {
    padding: var(--spacing-4xl) 0 0;
}

.map-container {
    margin-top: var(--spacing-2xl);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 3px solid var(--sky-blue);
}

.google-map {
    width: 100%;
    height: 400px;
}

.map-info-window {
    padding: var(--spacing-md);
    text-align: center;
}

.map-info-window h4 {
    color: var(--navy-blue);
    margin-bottom: var(--spacing-sm);
}

.map-info-window p {
    color: var(--steel-blue);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--navy-blue);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    box-shadow: var(--shadow-lg);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background-color: var(--sky-blue);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--sky-blue);
    border-top: 2px solid var(--navy-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
*:focus {
    outline: 2px solid var(--sky-blue);
    outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--navy-blue);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .cookie-banner,
    .scroll-to-top,
    .btn,
    .swiper-pagination,
    .swiper-button-next,
    .swiper-button-prev {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero {
        height: auto !important;
        min-height: auto !important;
    }

    section {
        page-break-inside: avoid;
    }

    h1, h2, h3 {
        page-break-after: avoid;
    }
}
