// ===== CONTACT FORM JAVASCRIPT =====
// Contact form validation and submission using Alpine.js

// Alpine.js component for contact form
function contactForm() {
    return {
        form: {
            name: '',
            email: '',
            phone: '',
            company: '',
            service: '',
            budget: '',
            message: '',
            privacy: false,
            newsletter: false
        },
        errors: {},
        isSubmitting: false,
        message: {
            text: '',
            type: ''
        },
        
        // Form validation
        validateForm() {
            this.errors = {};
            
            // Name validation
            if (!this.form.name.trim()) {
                this.errors.name = 'Il nome è obbligatorio';
            } else if (this.form.name.trim().length < 2) {
                this.errors.name = 'Il nome deve contenere almeno 2 caratteri';
            }
            
            // Email validation
            if (!this.form.email.trim()) {
                this.errors.email = 'L\'email è obbligatoria';
            } else if (!this.isValidEmail(this.form.email)) {
                this.errors.email = 'Inserisci un\'email valida';
            }
            
            // Service validation
            if (!this.form.service) {
                this.errors.service = 'Seleziona un servizio';
            }
            
            // Message validation
            if (!this.form.message.trim()) {
                this.errors.message = 'Il messaggio è obbligatorio';
            } else if (this.form.message.trim().length < 10) {
                this.errors.message = 'Il messaggio deve contenere almeno 10 caratteri';
            }
            
            // Privacy validation
            if (!this.form.privacy) {
                this.errors.privacy = 'Devi accettare la Privacy Policy';
            }
            
            return Object.keys(this.errors).length === 0;
        },
        
        // Email validation helper
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        // Form submission
        async submitForm() {
            if (!this.validateForm()) {
                this.showMessage('Correggi gli errori nel form', 'error');
                return;
            }
            
            this.isSubmitting = true;
            this.message = { text: '', type: '' };
            
            try {
                // Simulate API call (replace with actual endpoint)
                await this.sendFormData();
                
                this.showMessage('Messaggio inviato con successo! Ti contatteremo presto.', 'success');
                this.resetForm();
                
                // Track successful submission
                if (typeof trackEvent === 'function') {
                    trackEvent('contact_form', 'submit_success', this.form.service);
                }
                
            } catch (error) {
                console.error('Form submission error:', error);
                this.showMessage('Errore nell\'invio del messaggio. Riprova più tardi.', 'error');
                
                // Track failed submission
                if (typeof trackEvent === 'function') {
                    trackEvent('contact_form', 'submit_error', error.message);
                }
            } finally {
                this.isSubmitting = false;
            }
        },
        
        // Simulate form data sending (replace with actual API call)
        async sendFormData() {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    // Simulate random success/failure for demo
                    if (Math.random() > 0.1) { // 90% success rate
                        resolve({ success: true });
                    } else {
                        reject(new Error('Server error'));
                    }
                }, 2000);
            });
        },
        
        // Show message to user
        showMessage(text, type) {
            this.message = { text, type };
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    this.message = { text: '', type: '' };
                }, 5000);
            }
        },
        
        // Reset form
        resetForm() {
            this.form = {
                name: '',
                email: '',
                phone: '',
                company: '',
                service: '',
                budget: '',
                message: '',
                privacy: false,
                newsletter: false
            };
            this.errors = {};
        }
    }
}

// Google Maps initialization
function initMap() {
    // Company location (Milan, Italy)
    const companyLocation = { lat: 45.4642, lng: 9.1900 };
    
    // Map options
    const mapOptions = {
        zoom: 15,
        center: companyLocation,
        styles: [
            {
                "featureType": "all",
                "elementType": "geometry.fill",
                "stylers": [
                    {
                        "weight": "2.00"
                    }
                ]
            },
            {
                "featureType": "all",
                "elementType": "geometry.stroke",
                "stylers": [
                    {
                        "color": "#9c9c9c"
                    }
                ]
            },
            {
                "featureType": "all",
                "elementType": "labels.text",
                "stylers": [
                    {
                        "visibility": "on"
                    }
                ]
            },
            {
                "featureType": "landscape",
                "elementType": "all",
                "stylers": [
                    {
                        "color": "#f2f2f2"
                    }
                ]
            },
            {
                "featureType": "landscape",
                "elementType": "geometry.fill",
                "stylers": [
                    {
                        "color": "#ffffff"
                    }
                ]
            },
            {
                "featureType": "landscape.man_made",
                "elementType": "geometry.fill",
                "stylers": [
                    {
                        "color": "#ffffff"
                    }
                ]
            },
            {
                "featureType": "poi",
                "elementType": "all",
                "stylers": [
                    {
                        "visibility": "off"
                    }
                ]
            },
            {
                "featureType": "road",
                "elementType": "all",
                "stylers": [
                    {
                        "saturation": -100
                    },
                    {
                        "lightness": 45
                    }
                ]
            },
            {
                "featureType": "road",
                "elementType": "geometry.fill",
                "stylers": [
                    {
                        "color": "#eeeeee"
                    }
                ]
            },
            {
                "featureType": "road",
                "elementType": "labels.text.fill",
                "stylers": [
                    {
                        "color": "#7b7b7b"
                    }
                ]
            },
            {
                "featureType": "road",
                "elementType": "labels.text.stroke",
                "stylers": [
                    {
                        "color": "#ffffff"
                    }
                ]
            },
            {
                "featureType": "road.highway",
                "elementType": "all",
                "stylers": [
                    {
                        "visibility": "simplified"
                    }
                ]
            },
            {
                "featureType": "road.arterial",
                "elementType": "labels.icon",
                "stylers": [
                    {
                        "visibility": "off"
                    }
                ]
            },
            {
                "featureType": "transit",
                "elementType": "all",
                "stylers": [
                    {
                        "visibility": "off"
                    }
                ]
            },
            {
                "featureType": "water",
                "elementType": "all",
                "stylers": [
                    {
                        "color": "#46bcec"
                    },
                    {
                        "visibility": "on"
                    }
                ]
            },
            {
                "featureType": "water",
                "elementType": "geometry.fill",
                "stylers": [
                    {
                        "color": "#a3cce9"
                    }
                ]
            }
        ]
    };
    
    // Create map
    const map = new google.maps.Map(document.getElementById('google-map'), mapOptions);
    
    // Custom marker
    const marker = new google.maps.Marker({
        position: companyLocation,
        map: map,
        title: 'Web Agency',
        icon: {
            url: 'assets/images/map-marker.png',
            scaledSize: new google.maps.Size(40, 40)
        }
    });
    
    // Info window
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div class="map-info-window">
                <h4>Web Agency</h4>
                <p>Via Roma 123<br>20121 Milano, Italia</p>
                <p><strong>Tel:</strong> +39 02 1234567</p>
                <p><strong>Email:</strong> <EMAIL></p>
            </div>
        `
    });
    
    // Show info window on marker click
    marker.addListener('click', function() {
        infoWindow.open(map, marker);
    });
    
    // Track map interaction
    map.addListener('click', function() {
        if (typeof trackEvent === 'function') {
            trackEvent('map_interaction', 'click');
        }
    });
}

// Initialize contact page functionality
$(document).ready(function() {
    // Animate contact elements on scroll
    if ($('.contact-section').length) {
        gsap.utils.toArray('.contact-item').forEach((item, index) => {
            gsap.fromTo(item, 
                { opacity: 0, x: -30 },
                {
                    opacity: 1,
                    x: 0,
                    duration: 0.6,
                    delay: index * 0.1,
                    scrollTrigger: {
                        trigger: item,
                        start: 'top 80%',
                        end: 'bottom 20%',
                        toggleActions: 'play none none reverse'
                    }
                }
            );
        });
        
        // Animate contact form
        gsap.fromTo('.contact-form-wrapper', 
            { opacity: 0, y: 30 },
            {
                opacity: 1,
                y: 0,
                duration: 0.8,
                scrollTrigger: {
                    trigger: '.contact-form-wrapper',
                    start: 'top 80%',
                    end: 'bottom 20%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    }
    
    // Track form field interactions
    $('input, select, textarea').on('focus', function() {
        const fieldName = $(this).attr('name');
        if (typeof trackEvent === 'function') {
            trackEvent('form_interaction', 'field_focus', fieldName);
        }
    });
});
