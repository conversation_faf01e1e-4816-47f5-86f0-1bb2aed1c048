# Image Requirements for Web Agency Website

This document outlines the image requirements for the web agency website. All images should be optimized for web use and follow the specified dimensions for best performance.

## Required Images

### Logo and Branding
- **logo.png** - Main logo (200x60px) - Navy blue logo for light backgrounds
- **logo-white.png** - White logo (200x60px) - White logo for dark backgrounds
- **favicon.ico** - Favicon (32x32px) - Website icon for browser tabs
- **apple-touch-icon.png** - Apple touch icon (180x180px) - iOS home screen icon

### Hero Section Images
- **hero-1.jpg** - Hero slide 1 (1920x1080px) - Modern office/technology theme
- **hero-2.jpg** - Hero slide 2 (1920x1080px) - Marketing/advertising theme
- **hero-3.jpg** - Hero slide 3 (1920x1080px) - Development/coding theme

### Project Portfolio Images
- **project-1.jpg** - E-commerce project (600x400px) - Fashion e-commerce screenshot
- **project-1-1.jpg** - Project 1 gallery image 1 (800x600px)
- **project-1-2.jpg** - Project 1 gallery image 2 (800x600px)
- **project-1-3.jpg** - Project 1 gallery image 3 (800x600px)

- **project-2.jpg** - Management system project (600x400px) - Dashboard screenshot
- **project-2-1.jpg** - Project 2 gallery image 1 (800x600px)
- **project-2-2.jpg** - Project 2 gallery image 2 (800x600px)
- **project-2-3.jpg** - Project 2 gallery image 3 (800x600px)

- **project-3.jpg** - Marketing campaign project (600x400px) - Analytics/charts
- **project-3-1.jpg** - Project 3 gallery image 1 (800x600px)
- **project-3-2.jpg** - Project 3 gallery image 2 (800x600px)
- **project-3-3.jpg** - Project 3 gallery image 3 (800x600px)

- **project-4.jpg** - Mobile app project (600x400px) - Mobile app interface
- **project-4-1.jpg** - Project 4 gallery image 1 (800x600px)
- **project-4-2.jpg** - Project 4 gallery image 2 (800x600px)
- **project-4-3.jpg** - Project 4 gallery image 3 (800x600px)

- **project-5.jpg** - Google Ads project (600x400px) - Advertising dashboard
- **project-5-1.jpg** - Project 5 gallery image 1 (800x600px)
- **project-5-2.jpg** - Project 5 gallery image 2 (800x600px)
- **project-5-3.jpg** - Project 5 gallery image 3 (800x600px)

- **project-6.jpg** - E-learning platform (600x400px) - Learning interface
- **project-6-1.jpg** - Project 6 gallery image 1 (800x600px)
- **project-6-2.jpg** - Project 6 gallery image 2 (800x600px)
- **project-6-3.jpg** - Project 6 gallery image 3 (800x600px)

### Team Member Photos
- **team-1.jpg** - Marco Rossi (400x400px) - Professional headshot
- **team-2.jpg** - Laura Bianchi (400x400px) - Professional headshot
- **team-3.jpg** - Alessandro Verdi (400x400px) - Professional headshot
- **team-4.jpg** - Giulia Neri (400x400px) - Professional headshot

### About Page Images
- **about-story.jpg** - Company story image (800x600px) - Office/team working

### Social Media and SEO
- **og-image.jpg** - Open Graph image (1200x630px) - Social media sharing
- **twitter-card.jpg** - Twitter card image (1200x600px) - Twitter sharing

### Maps and Contact
- **map-marker.png** - Custom map marker (40x40px) - Company location marker

## Image Optimization Guidelines

### File Formats
- **JPEG** - Use for photographs and complex images with many colors
- **PNG** - Use for logos, icons, and images with transparency
- **WebP** - Consider using WebP format for better compression (with JPEG/PNG fallbacks)

### Compression
- Compress all images to reduce file size while maintaining quality
- Target file sizes:
  - Hero images: < 200KB each
  - Project images: < 100KB each
  - Team photos: < 50KB each
  - Icons and logos: < 20KB each

### Responsive Images
- Consider creating multiple sizes for responsive design:
  - Small (mobile): 480px width
  - Medium (tablet): 768px width
  - Large (desktop): 1200px width

### Alt Text Requirements
All images should have descriptive alt text for accessibility:
- Logo: "Web Agency Logo"
- Hero images: Describe the scene/content
- Project images: "Project name - description"
- Team photos: "Team member name - role"

## Placeholder Images
For development purposes, you can use placeholder images from:
- https://picsum.photos/ - Random photos
- https://via.placeholder.com/ - Solid color placeholders
- https://unsplash.com/ - High-quality stock photos

## Color Palette Reference
When selecting or creating images, consider the website's color palette:
- Ice Blue: #e8f3f7
- Sky Blue: #a3cce9
- Dusty Blue: #85abc4
- Steel Blue: #4b6281
- Navy Blue: #22365b

## Performance Considerations
- Use lazy loading for images below the fold
- Implement proper caching headers
- Consider using a CDN for image delivery
- Optimize images for Core Web Vitals (LCP, CLS)
